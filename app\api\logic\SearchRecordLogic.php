<?php
namespace app\api\logic;

use app\common\basics\Logic;
use app\common\model\goods\Goods;
use app\common\model\SearchRecord;
use app\common\server\ConfigServer;
use think\facade\Cache;
use think\facade\Db;

class SearchRecordLogic extends Logic
{
    // 词性权重配置
    // '品牌', '品类', '功能功效', '人群', '场景'
    protected $tagWeights = [
        // 高优先级
        '产品-品牌' => 30,
        '产品类型-简单' => 25,
        '产品类型属性词' => 22,
        '促销词' => 20,
        '产品类型修饰词' => 18,
        '产品类型修饰词匹配分词' => 18,

        // 中优先级
        '专有名词-中药材' => 15,
        '专有名词-药品' => 15,
        '产品类型-统称' => 12,
        '产品类型通用修饰词' => 10,
        '文体娱乐类-书文课程类' => 10,
        '文体娱乐类-游戏类' => 10,
        '人名-中国人名' => 8,
        '人名-欧美人名' => 8,

        // 低优先级
        '基本词-中文' => 5,
        '基本词-中文词组' => 5,
        '缩略-中文缩略' => 3,
        '缩略-英文缩略的中文全称' => 3,
        '网站-其他' => 2,
        '网站-机构名' => 2,
        '区划-无后缀区划' => 1,
        '区划-国家及地区' => 1,
        '错误词' => 0,
        '未打标新词' => 0,

        // 排除词
        '不好词汇' => -1,
        '色情词汇-中文' => -1,
        '前缀非正品词类' => -1,
        '后缀非正品词类' => -1,
        '仿品词类' => -1,
    ];
    public static function lists($userId)
    {
        // 热搜关键词
        $hotLists= ConfigServer::get('hot_search', 'hot_keyword', []);

        // 用户历史搜索记录
        if($userId) {
            // 已登录
            $where = [
                'del' => 0,
                'user_id' => $userId
            ];
            $order = [
                'update_time' => 'desc',
                'id' => 'desc'
            ];
            $historyLists = SearchRecord::where($where)
                ->order($order)
                ->limit(10)
                ->column('keyword');
        }else{
            // 未登录
            $historyLists = [];
        }

        return [
            'history_lists' => $historyLists,
            'hot_lists' => $hotLists
        ];
    }


    /**
     * 获取热搜关键词列表
     *
     * @param int $userId 用户ID（保留参数以保持接口一致性）
     * @return array 包含热搜关键词的数组
     */
    public static function findlists($userId)
    {
        // 使用缓存来提高性能，不同用户共享相同的热搜关键词
        $cacheKey = 'hot_search_keywords_v2';
        $hotKeywords = Cache::get($cacheKey);

        if (!$hotKeywords) {
            // 1. 从配置中获取热搜关键词
            $configHotKeywords = ConfigServer::get('hot_search', 'hot_keyword', []);

            // 2. 从SearchRecord表中获取实际的热门搜索关键词，过滤测试相关关键词
            $dbHotKeywords = SearchRecord::where('del', 0)
                ->where('keyword', 'not like', '%测试%') // 过滤包含"测试"的关键词
                ->where('keyword', 'not like', '%test%') // 过滤包含"test"的关键词
                ->field('keyword, COUNT(*) as search_count, MAX(update_time) as last_update')
                ->group('keyword')
                ->having('search_count >= 2') // 至少被搜索2次
                ->order('search_count DESC, last_update DESC')
                ->limit(30)
                ->select()
                ->toArray();

            // 提取关键词
            $dbKeywords = array_column($dbHotKeywords, 'keyword');

            // 3. 合并配置和数据库中的热搜关键词，去重
            $allHotKeywords = array_values(array_unique(array_merge($configHotKeywords, $dbKeywords)));

            // 4. 过滤和验证热搜关键词
            $validHotKeywords = [];
            $meiliCheckedCount = 0;

            foreach ($allHotKeywords as $keyword) {
                // 过滤太短的关键词
                if (mb_strlen($keyword, 'UTF-8') < 2) {
                    continue;
                }

                // 限制MeiliSearch查询次数，避免性能问题
                if ($meiliCheckedCount < 15) {
                    try {
                        // 验证关键词是否能匹配到商品
                        $meiliCount = self::countGoodsInMeili($keyword);
                        if ($meiliCount > 0) {
                            $validHotKeywords[] = $keyword;
                            $meiliCheckedCount++;
                        }
                    } catch (\Exception $exception) {
                        // 记录错误日志
                        \think\facade\Log::error("MeiliSearch查询失败: " . $exception->getMessage());

                        // 如果MeiliSearch查询失败，仍然保留配置中的关键词
                        if (in_array($keyword, $configHotKeywords)) {
                            $validHotKeywords[] = $keyword;
                        }
                    }
                } else {
                    // 超过查询限制后，直接添加配置中的关键词
                    if (in_array($keyword, $configHotKeywords)) {
                        $validHotKeywords[] = $keyword;
                    }
                }

                // 限制关键词数量
                if (count($validHotKeywords) >= 15) {
                    break;
                }
            }

            // 5. 如果有效关键词不足10个，添加一些热门商品类别或品牌
            if (count($validHotKeywords) < 10) {
                // 尝试获取热门商品类别
                $hotCategories = Db::name('goods_category')
                    ->where('is_show', 1)
                    ->where('del', 0)
                    ->order('sort DESC')
                    ->limit(10 - count($validHotKeywords))
                    ->column('name');

                // 如果没有足够的类别，尝试获取热门品牌
                if (count($hotCategories) < (10 - count($validHotKeywords))) {
                    $hotBrands = Db::name('goods_brand')
                        ->where('is_show', 1)
                        ->where('del', 0)
                        ->order('sort DESC')
                        ->limit(10 - count($validHotKeywords) - count($hotCategories))
                        ->column('name');

                    $validHotKeywords = array_merge($validHotKeywords, $hotCategories, $hotBrands);
                } else {
                    $validHotKeywords = array_merge($validHotKeywords, $hotCategories);
                }
            }

            // 6. 确保关键词数量不超过15个
            $validHotKeywords = array_slice($validHotKeywords, 0, 15);

            // 缓存结果，有效期1小时
            $hotKeywords = $validHotKeywords;
            Cache::set($cacheKey, $hotKeywords, 3600);
        }

        // 7. 返回结果，保持原有的数据格式
        return [
            'find_lists' => $hotKeywords
        ];
    }

    public static function getBubble($userId)
    {
        // 热搜关键词
        $hotLists= ConfigServer::get('hot_search', 'hot_keyword', []);
        //获取足迹
        // 用户历史搜索记录
        if($userId) {
            // 已登录
            $where = [
                'del' => 0,
                'user_id' => $userId
            ];
            $order = [
                'update_time' => 'desc',
                'id' => 'desc'
            ];
            $historyLists = SearchRecord::where($where)
                ->order($order)
                ->limit(10)
                ->column('keyword');
        }else{
            // 未登录
            $where = [
                'del' => 0
            ];
            $order = [
                'count' => 'desc'
            ];

            $historyLists = SearchRecord::where($where)
                ->order($order)
                ->limit(10)
                ->group('keyword')
                ->column('keyword');
        }
        $list=array_merge($historyLists,$hotLists);
        return [
            'lists' => $list,
        ];
    }
    /**
     * 清空搜索历史
     */
    public static function  clear($userId)
    {
        try {
            $data = [
                'update_time' => time(),
                'del' => 1
            ];
            $result = Db::name('search_record')->where('user_id', $userId)->update($data);

            return true;
        } catch(\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    public static function hotlists($userId)
    {
        // 已登录
        $where = [
            'del' => 0
        ];
        $order = [
            'count' => 'desc'
        ];
        $historyLists = SearchRecord::where($where)
            ->where('keyword', 'not like', '%测试%') // 过滤包含"测试"的关键词
            ->where('keyword', 'not like', '%test%') // 过滤包含"test"的关键词
            ->order($order)
            ->limit(10)
            ->column('keyword');

        // 获取热门关键词
        $hotLists = ConfigServer::get('hot_search', 'hot_keyword', []);

        return [
            'history_lists' => $historyLists,
            'hot_lists' => $hotLists
        ];
    }

    /**
     * 获取热门搜索关键词
     * @param int $limit 返回数量限制
     * @return array 热门关键词列表
     */
    public static function getHotKeywords($limit = 10)
    {
        // 从配置中获取热门关键词
        $hotKeywords = ConfigServer::get('hot_search', 'hot_keyword', []);

        // 如果配置中没有热门关键词，从搜索记录中获取
        if (empty($hotKeywords)) {
            $where = [
                'del' => 0
            ];
            $order = [
                'count' => 'desc'
            ];
            $hotKeywords = SearchRecord::where($where)
                ->order($order)
                ->limit($limit)
                ->column('keyword');
        }

        // 格式化为前端需要的格式
        $result = [];
        foreach ($hotKeywords as $keyword) {
            $result[] = [
                'title' => $keyword,
                'name' => $keyword
            ];
        }

        return array_slice($result, 0, $limit);
    }


    public static function searchGoods($keyword, $userId)
    {
        if (empty($keyword)) {
            return self::getHotKeywords(10);
        }

        // 记录用户搜索关键词（如果用户已登录）
        if ($userId) {
            GoodsLogic::recordKeyword($keyword, $userId);
        }

        // 使用新的SearchSuggestionService获取更智能的搜索候选词
        try {
            $suggestionService = new \app\common\library\SearchSuggestionService();
            $suggestions = $suggestionService->getSuggestions($keyword, 10);

            // 如果新服务返回了有效的候选词，直接使用
            if (!empty($suggestions)) {
                // 确保返回的是包含title和name字段的对象数组，并且进行高亮处理和长度限制
                $formattedSuggestions = [];
                foreach ($suggestions as $suggestion) {
                    // 获取候选词文本
                    $text = '';
                    if (is_array($suggestion)) {
                        if (isset($suggestion['name'])) {
                            $text = $suggestion['name'];
                        } elseif (isset($suggestion['title'])) {
                            // 如果只有title字段，去除HTML标签获取纯文本
                            $text = strip_tags($suggestion['title']);
                        }
                    } else {
                        $text = $suggestion;
                    }

                    // 限制文本长度，最多15个字符
                    $shortText = mb_strlen($text, 'UTF-8') > 15 ? mb_substr($text, 0, 15, 'UTF-8') : $text;

                    // 高亮处理关键词
                    $pattern = '/(' . preg_quote($keyword, '/') . ')/iu';
                    $replacement = '<b style="color: red;">$1</b>';

                    // 确保关键词被高亮显示
                    if (mb_stripos($shortText, $keyword) !== false) {
                        $highlighted = preg_replace($pattern, $replacement, $shortText);
                    } else {
                        // 如果关键词不在文本中，强制添加并高亮
                        $highlighted = '<b style="color: red;">' . $keyword . '</b>' . $shortText;
                    }

                    $formattedSuggestions[] = [
                        'title' => $highlighted,
                        'name' => $shortText
                    ];
                }
                return $formattedSuggestions;
            }
        } catch (\Exception $e) {
            // 记录错误，但不中断流程
            \think\facade\Log::error('SearchSuggestionService调用失败: ' . $e->getMessage());
        }

        // 如果新服务未返回有效候选词，使用原有逻辑
        // 使用阿里云NLP服务获取中心词和品类词
        $centerWords = self::getCenterAndCategoryWords($keyword);

        // 如果找到中心词或品类词，优先使用它们进行搜索
        if (!empty($centerWords)) {
            $goodsList = self::searchGoodsByCenterWords($keyword, $centerWords);

            // 如果找到结果，直接返回
            if (!empty($goodsList)) {
                // 确保返回的是包含title和name字段的对象数组
                $formattedSuggestions = self::highlightKeywords($goodsList, array_merge([$keyword], $centerWords));

                // 再次检查格式
                $result = [];
                foreach ($formattedSuggestions as $suggestion) {
                    // 如果已经是格式化的数据，直接使用
                    if (is_array($suggestion) && isset($suggestion['title']) && isset($suggestion['name'])) {
                        $result[] = $suggestion;
                    } else {
                        // 否则，格式化为包含title和name字段的对象
                        $result[] = [
                            'title' => $suggestion,
                            'name' => $suggestion
                        ];
                    }
                }

                return $result;
            }
        }

        // 如果中心词搜索没有结果，回退到传统分词搜索
        $words = self::getSegmentedWords($keyword);
        [$where, $highlightWords] = self::buildSearchConditions($keyword, $words);

        $goodsList = Goods::where($where)
            ->limit(10)
            ->column('name');

        // 确保返回的是包含title和name字段的对象数组
        $formattedSuggestions = self::highlightKeywords($goodsList, $highlightWords);

        // 再次检查格式
        $result = [];
        foreach ($formattedSuggestions as $suggestion) {
            // 如果已经是格式化的数据，直接使用
            if (is_array($suggestion) && isset($suggestion['title']) && isset($suggestion['name'])) {
                $result[] = $suggestion;
            } else {
                // 否则，格式化为包含title和name字段的对象
                $result[] = [
                    'title' => $suggestion,
                    'name' => $suggestion
                ];
            }
        }

        return $result;
    }

    /**
     * 获取中心词和品类词
     * @param string $keyword 搜索关键词
     * @return array 中心词和品类词列表
     */
    protected static function getCenterAndCategoryWords($keyword)
    {
        $words = [];

        try {
            // 初始化阿里云NLP服务
            $aliNlp = new \app\common\library\AliNlpService();

            // 1. 使用中心词提取-中文电商API
            $keywordResult = $aliNlp->keywordExtractEcom($keyword);

            if (isset($keywordResult['keywords']) && !empty($keywordResult['keywords'])) {
                $words = array_merge($words, $keywordResult['keywords']);
            }

            // 2. 使用命名实体识别-电商API
            $nerResult = $aliNlp->nerEcom($keyword);

            if (isset($nerResult['words']) && !empty($nerResult['words'])) {
                $words = array_merge($words, $nerResult['words']);
            }

            // 3. 使用分词作为替代
            $segmentResult = $aliNlp->segment($keyword);
            if (isset($segmentResult['words']) && !empty($segmentResult['words'])) {
                foreach ($segmentResult['words'] as $word) {
                    if (mb_strlen($word, 'UTF-8') >= 2) {
                        $words[] = $word;
                    }
                }
            }

            // 4. 使用分词和命名实体识别来提取属性
            $segmentResult = $aliNlp->segment($keyword);
            if (isset($segmentResult['words']) && !empty($segmentResult['words'])) {
                foreach ($segmentResult['words'] as $word) {
                    if (mb_strlen($word, 'UTF-8') >= 2) {
                        $words[] = $word;
                    }
                }
            }

            // 去重并过滤掉长度小于2的词
            $words = array_unique($words);
            $words = array_filter($words, function($word) {
                return mb_strlen($word, 'UTF-8') >= 2;
            });

        } catch (\Exception $e) {
            \think\facade\Log::error('获取中心词和品类词失败: ' . $e->getMessage());
        }

        return $words;
    }

    /**
     * 基于中心词和品类词搜索商品
     * @param string $keyword 原始搜索关键词
     * @param array $centerWords 中心词和品类词列表
     * @return array 商品列表
     */
    protected static function searchGoodsByCenterWords($keyword, $centerWords)
    {
        $goodsList = [];

        // 构建查询条件
        $where = [
            ['del', '=', 0],
            ['status', '=', 1],
            ['audit_status', '=', 1]  // 添加审核状态条件
        ];

        // 添加中心词和品类词条件
        $orWhere = [];
        foreach ($centerWords as $word) {
            $orWhere[] = ['name', 'like', '%'.$word.'%'];
        }

        // 添加原始关键词条件
        $orWhere[] = ['name', 'like', '%'.$keyword.'%'];

        // 执行查询
        $goods = Goods::where($where)
            ->where(function($query) use ($orWhere) {
                foreach ($orWhere as $condition) {
                    $query->whereOr([$condition]);
                }
            })
            ->limit(10)
            ->column('name');

        if (!empty($goods)) {
            $goodsList = $goods;
        }

        return $goodsList;
    }

    protected static function getSegmentedWords($keyword)
    {
        if (mb_strlen($keyword, 'UTF-8') < 2) {
            return ['words' => [$keyword]];
        }

        return Cache::remember('search_'.$keyword, function() use ($keyword) {
            return Alisegment($keyword) ?: ['words' => [$keyword]];
        });
    }

    protected static function   buildSearchConditions($keyword, $words)
    {
        $where = [
            ['del', '=', 0],
            ['status', '=', 1],
            ['audit_status', '=', 1]  // 添加审核状态条件
        ];

        $highlightWords = [];

        if (mb_strlen($keyword, 'UTF-8') < 2) {
            $where[] = ['name', 'like', $keyword.'%'];
            $highlightWords[] = $keyword;
            return [$where, $highlightWords];
        }

        foreach ($words['words'] as $val) {
            $trimmed = trim(urldecode($val));
            if (!empty($trimmed)) {
                $where[] = ['name', 'like', '%'.$trimmed.'%'];
                $highlightWords[] = $trimmed;
            }
        }

        return [$where, $highlightWords];
    }

    protected static function highlightKeywords($items, $keywords)
    {
        return array_map(function($item) use ($keywords) {
            // 提取商品名称中的关键部分，不加省略号
            // 限制商品名称长度，最多15个字符
            $shortItem = mb_strlen($item, 'UTF-8') > 15 ? mb_substr($item, 0, 15, 'UTF-8') : $item;

            // 如果有空格，只取第一部分
            if (strpos($shortItem, ' ') !== false) {
                $parts = explode(' ', $shortItem);
                $shortItem = $parts[0];

                // 如果第一部分太短，尝试添加第二部分
                if (count($parts) > 1 && mb_strlen($shortItem, 'UTF-8') < 5) {
                    $shortItem .= ' ' . $parts[1];
                }
            }

            // 高亮处理
            $highlighted = $shortItem;

            // 确保至少有一个关键词被高亮
            $hasHighlight = false;

            foreach ($keywords as $word) {
                if (empty($word)) {
                    continue;
                }

                // 使用正则表达式进行大小写不敏感的匹配
                $pattern = '/(' . preg_quote($word, '/') . ')/iu';
                $replacement = '<b style="color: red;">$1</b>';

                // 检查关键词是否在文本中
                if (mb_stripos($highlighted, $word) !== false) {
                    $highlighted = preg_replace($pattern, $replacement, $highlighted);
                    $hasHighlight = true;
                }
            }

            // 如果没有任何关键词被高亮，强制高亮第一个关键词
            if (!$hasHighlight && !empty($keywords)) {
                $mainKeyword = $keywords[0];
                if (!empty($mainKeyword)) {
                    $highlighted = '<b style="color: red;">' . $mainKeyword . '</b>' . $shortItem;
                }
            }

            return [
                'title' => $highlighted, // 高亮后的简短文本
                'name' => $shortItem    // 简短的原始文本
            ];
        }, $items);
    }



    /*
     * 获取用户热搜 - 仿照主流电商平台的热搜逻辑
     */
    public static function getSearchHot($userId){
        // 使用缓存提高性能
        $cacheKey = 'search_hot_v3_' . ($userId ? $userId : 'guest');
        $result = Cache::get($cacheKey);

        if (!$result) {
            $result = [];

            // 1. 全网热搜 - 基于多维度热度计算
            $globalHotData = self::getGlobalHotSearch();
            $result[] = [
                'name' => '全网热搜',
                'data' => $globalHotData
            ];

            // 2. 热搜工厂 - 基于商家综合评分
            $hotShopsData = self::getHotShops();
            $result[] = [
                'name' => '热搜工厂',
                'data' => $hotShopsData
            ];

            // 3. 如果用户已登录，添加个性化推荐
            if ($userId) {
                $personalizedData = self::getPersonalizedHotSearch($userId);
                if (!empty($personalizedData)) {
                    array_unshift($result, [
                        'name' => '为你推荐',
                        'data' => $personalizedData
                    ]);
                }
            }

            // 缓存5分钟
            Cache::set($cacheKey, $result, 300);
        }

        return $result;
    }

    /**
     * 获取全网热搜 - 多维度热度计算
     */
    private static function getGlobalHotSearch()
    {
        $cacheKey = 'global_hot_search_v3';
        $hotData = Cache::get($cacheKey);

        if (!$hotData) {
            // 获取最近7天的搜索数据，计算热度分数
            $sevenDaysAgo = time() - (7 * 24 * 3600);
            $threeDaysAgo = time() - (3 * 24 * 3600);
            $oneDayAgo = time() - (1 * 24 * 3600);

            $searchData = Db::name('search_record')
                ->field('keyword, COUNT(*) as search_count, SUM(count) as total_searches, MAX(update_time) as last_search')
                ->where('del', 0)
                ->where('update_time', '>=', $sevenDaysAgo)
                ->where('keyword', '<>', '')
                ->whereRaw('CHAR_LENGTH(keyword) >= 2') // 过滤太短的关键词
                ->group('keyword')
                ->having('total_searches >= 3') // 至少被搜索3次
                ->select()
                ->toArray();

            $hotKeywords = [];

            foreach ($searchData as $item) {
                $keyword = $item['keyword'];
                $totalSearches = intval($item['total_searches']);
                $lastSearch = intval($item['last_search']);

                // 计算时间衰减因子
                $timeDecay = 1.0;
                if ($lastSearch >= $oneDayAgo) {
                    $timeDecay = 1.5; // 最近1天的搜索加权
                } elseif ($lastSearch >= $threeDaysAgo) {
                    $timeDecay = 1.2; // 最近3天的搜索加权
                } elseif ($lastSearch < $sevenDaysAgo) {
                    $timeDecay = 0.5; // 超过7天的搜索降权
                }

                // 检查是否有对应商品
                $goodsCount = self::getGoodsCountByKeyword($keyword);
                if ($goodsCount == 0) {
                    continue; // 没有对应商品的关键词不展示
                }

                // 计算商品热度加权（基于销量）
                $goodsHotWeight = self::getGoodsHotWeight($keyword);

                // 综合热度分数 = 搜索次数 * 时间衰减 * 商品热度权重
                $hotScore = $totalSearches * $timeDecay * $goodsHotWeight;

                $hotKeywords[] = [
                    'keyword' => $keyword,
                    'hot_score' => $hotScore,
                    'search_count' => $totalSearches,
                    'goods_count' => $goodsCount,
                    'last_search' => $lastSearch
                ];
            }

            // 按热度分数排序
            usort($hotKeywords, function($a, $b) {
                return $b['hot_score'] <=> $a['hot_score'];
            });

            // 格式化输出，取前10个
            $hotData = [];
            $count = 0;
            foreach ($hotKeywords as $item) {
                if ($count >= 10) break;

                // 限制关键词显示长度，超过6个字符显示省略号
                $displayName = self::formatKeywordDisplay($item['keyword'], 6);

                $hotData[] = [
                    'name' => $displayName,
                    'type' => 0, // 0表示搜索关键词
                    'hot_score' => round($item['hot_score'], 2),
                    'trend' => self::getKeywordTrend($item['keyword']) // 获取趋势标识
                ];
                $count++;
            }

            // 如果热搜数量不足，补充配置的热门关键词
            if (count($hotData) < 10) {
                $configHotKeywords = ConfigServer::get('hot_search', 'hot_keyword', []);
                foreach ($configHotKeywords as $keyword) {
                    if (count($hotData) >= 10) break;

                    // 检查是否已存在
                    $exists = false;
                    foreach ($hotData as $existing) {
                        if ($existing['name'] == $keyword) {
                            $exists = true;
                            break;
                        }
                    }

                    if (!$exists && self::getGoodsCountByKeyword($keyword) > 0) {
                        $hotData[] = [
                            'name' => self::formatKeywordDisplay($keyword, 6),
                            'type' => 0,
                            'hot_score' => 0,
                            'trend' => 'stable'
                        ];
                    }
                }
            }

            // 缓存10分钟
            Cache::set($cacheKey, $hotData, 600);
        }

        return $hotData;
    }

    /**
     * 获取热搜工厂 - 基于商家综合评分
     */
    private static function getHotShops()
    {
        $cacheKey = 'hot_shops_v2';
        $hotShops = Cache::get($cacheKey);

        if (!$hotShops) {
            // 获取活跃商家，综合考虑访问量、推荐状态、商品数量等
            $shops = Db::name('shop')
                ->alias('s')
                ->leftJoin('goods g', 's.id = g.shop_id AND g.del = 0 AND g.status = 1')
                ->field('s.id, s.name, s.visited_num, s.is_recommend, COUNT(g.id) as goods_count,
                        SUM(IFNULL(g.sales_actual, 0) + IFNULL(g.sales_virtual, 0)) as total_sales')
                ->where('s.del', 0)
                ->where('s.is_run', 1)
                ->where('s.is_freeze', 0) // 确保商家未被冻结
                ->group('s.id')
                ->having('goods_count > 0') // 必须有商品
                ->select()
                ->toArray();

            $hotShopsData = [];
            foreach ($shops as $shop) {
                // 计算商家热度分数
                $visitScore = intval($shop['visited_num']) * 0.3; // 访问量权重30%
                $recommendScore = intval($shop['is_recommend']) * 50; // 推荐权重50分
                $goodsScore = intval($shop['goods_count']) * 2; // 商品数量权重
                $salesScore = intval($shop['total_sales']) * 0.1; // 销量权重

                $totalScore = $visitScore + $recommendScore + $goodsScore + $salesScore;

                $hotShopsData[] = [
                    'id' => intval($shop['id']),
                    'name' => self::formatKeywordDisplay($shop['name'], 8), // 商家名称稍微长一点，8个字符
                    'type' => 1, // 1表示商家
                    'hot_score' => round($totalScore, 2),
                    'goods_count' => intval($shop['goods_count']),
                    'is_recommend' => intval($shop['is_recommend'])
                ];
            }

            // 按热度分数排序，取前10个
            usort($hotShopsData, function($a, $b) {
                return $b['hot_score'] <=> $a['hot_score'];
            });

            $hotShops = array_slice($hotShopsData, 0, 10);

            // 缓存15分钟
            Cache::set($cacheKey, $hotShops, 900);
        }

        return $hotShops;
    }

    /**
     * 获取个性化热搜推荐
     */
    private static function getPersonalizedHotSearch($userId)
    {
        $cacheKey = 'personalized_hot_' . $userId;
        $personalizedData = Cache::get($cacheKey);

        if (!$personalizedData) {
            $personalizedData = [];

            // 1. 基于用户搜索历史的相关推荐
            $userSearchHistory = Db::name('search_record')
                ->where('user_id', $userId)
                ->where('del', 0)
                ->where('update_time', '>=', time() - (30 * 24 * 3600)) // 最近30天
                ->order('update_time', 'desc')
                ->limit(5)
                ->column('keyword');

            // 2. 基于用户足迹的商品分类推荐
            $userCategories = Db::name('footprint_record')
                ->alias('fr')
                ->leftJoin('goods g', 'fr.foreign_id = g.id AND fr.type = 1') // type=1表示商品足迹
                ->leftJoin('goods_category gc', 'g.first_cate_id = gc.id OR g.second_cate_id = gc.id')
                ->where('fr.user_id', $userId)
                ->where('fr.create_time', '>=', time() - (7 * 24 * 3600)) // 最近7天
                ->where('g.del', 0)
                ->where('g.status', 1)
                ->where('gc.del', 0)
                ->whereNotNull('gc.name')
                ->group('gc.name')
                ->orderRaw('COUNT(*) DESC')
                ->limit(3)
                ->column('gc.name');

            // 3. 基于用户收藏的商品关键词
            $collectKeywords = Db::name('goods_collect')
                ->alias('gc')
                ->leftJoin('goods g', 'gc.goods_id = g.id')
                ->where('gc.user_id', $userId)
                ->where('g.del', 0)
                ->where('g.status', 1)
                ->order('gc.create_time', 'desc')
                ->limit(3)
                ->column('g.name');

            // 合并所有推荐关键词
            $allRecommendKeywords = array_merge($userSearchHistory, $userCategories, $collectKeywords);
            $allRecommendKeywords = array_unique(array_filter($allRecommendKeywords));

            // 如果用户数据不足，添加一些基于用户可能感兴趣的通用推荐
            if (count($allRecommendKeywords) < 3) {
                $generalRecommends = ['健康食品', '康复医疗', '适老家居', '护理用品'];
                $allRecommendKeywords = array_merge($allRecommendKeywords, $generalRecommends);
                $allRecommendKeywords = array_unique($allRecommendKeywords);
            }

            // 为每个关键词计算个性化分数
            foreach ($allRecommendKeywords as $keyword) {
                if (mb_strlen($keyword, 'UTF-8') < 2) continue;

                // 检查是否有对应商品
                $goodsCount = self::getGoodsCountByKeyword($keyword);
                if ($goodsCount == 0) continue;

                $personalizedData[] = [
                    'name' => self::formatKeywordDisplay($keyword, 6),
                    'type' => 0,
                    'hot_score' => 0,
                    'trend' => 'personalized'
                ];

                if (count($personalizedData) >= 6) break; // 最多6个个性化推荐
            }

            // 缓存30分钟
            Cache::set($cacheKey, $personalizedData, 1800);
        }

        return $personalizedData;
    }

    /**
     * 格式化关键词显示，限制长度并添加省略号
     * @param string $keyword 原始关键词
     * @param int $maxLength 最大显示长度（字符数）
     * @return string 格式化后的关键词
     */
    private static function formatKeywordDisplay($keyword, $maxLength = 6)
    {
        if (mb_strlen($keyword, 'UTF-8') <= $maxLength) {
            return $keyword;
        }

        return mb_substr($keyword, 0, $maxLength, 'UTF-8') . '...';
    }

    /**
     * 根据关键词获取商品数量
     */
    private static function getGoodsCountByKeyword($keyword)
    {
        try {
            $count = Db::name('goods')
                ->where(function($query) use ($keyword) {
                    $query->whereOr('name', 'like', '%' . $keyword . '%')
                          ->whereOr('remark', 'like', '%' . $keyword . '%')
                          ->whereOr('split_word', 'like', '%' . $keyword . '%');
                })
                ->where('del', 0)
                ->where('status', 1)
                ->where('audit_status', 1)
                ->count();
            return $count;
        } catch (\Exception $e) {
            \think\facade\Log::error("获取商品数量失败: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * 根据关键词获取商品热度权重
     */
    private static function getGoodsHotWeight($keyword)
    {
        try {
            $goods = Db::name('goods')
                ->field('sales_actual, sales_virtual, is_hot, is_recommend')
                ->where(function($query) use ($keyword) {
                    $query->whereOr('name', 'like', '%' . $keyword . '%')
                          ->whereOr('remark', 'like', '%' . $keyword . '%')
                          ->whereOr('split_word', 'like', '%' . $keyword . '%');
                })
                ->where('del', 0)
                ->where('status', 1)
                ->where('audit_status', 1)
                ->orderRaw('(sales_actual + sales_virtual) DESC')
                ->limit(10) // 取前10个商品计算平均热度
                ->select()
                ->toArray();

            if (empty($goods)) {
                return 1.0; // 默认权重
            }

            $totalWeight = 0;
            $count = 0;

            foreach ($goods as $item) {
                $salesWeight = (intval($item['sales_actual']) + intval($item['sales_virtual'])) * 0.01;
                $hotWeight = intval($item['is_hot']) ? 1.2 : 1.0;
                $recommendWeight = intval($item['is_recommend']) ? 1.1 : 1.0;

                $weight = max(1.0, $salesWeight * $hotWeight * $recommendWeight);
                $totalWeight += $weight;
                $count++;
            }

            return $count > 0 ? ($totalWeight / $count) : 1.0;
        } catch (\Exception $e) {
            \think\facade\Log::error("获取商品热度权重失败: " . $e->getMessage());
            return 1.0;
        }
    }

    /**
     * 获取关键词趋势
     */
    private static function getKeywordTrend($keyword)
    {
        try {
            $threeDaysAgo = time() - (3 * 24 * 3600);
            $sevenDaysAgo = time() - (7 * 24 * 3600);

            // 获取最近3天的搜索次数
            $recentCount = Db::name('search_record')
                ->where('keyword', $keyword)
                ->where('del', 0)
                ->where('update_time', '>=', $threeDaysAgo)
                ->sum('count');

            // 获取3-7天前的搜索次数
            $previousCount = Db::name('search_record')
                ->where('keyword', $keyword)
                ->where('del', 0)
                ->where('update_time', '>=', $sevenDaysAgo)
                ->where('update_time', '<', $threeDaysAgo)
                ->sum('count');

            if ($previousCount == 0) {
                return $recentCount > 0 ? 'rising' : 'stable';
            }

            $growthRate = ($recentCount - $previousCount) / $previousCount;

            if ($growthRate > 0.5) {
                return 'hot'; // 热门上升
            } elseif ($growthRate > 0.2) {
                return 'rising'; // 上升
            } elseif ($growthRate < -0.2) {
                return 'falling'; // 下降
            } else {
                return 'stable'; // 稳定
            }
        } catch (\Exception $e) {
            \think\facade\Log::error("获取关键词趋势失败: " . $e->getMessage());
            return 'stable';
        }
    }

    /**
     * 使用MeiliSearch搜索商品
     *
     * @param string $keyword 搜索关键词
     * @param int|null $userId 用户ID，可以为null
     * @param array $options 搜索选项，如过滤器、排序、分页等
     * @return array 搜索结果
     */
    public static function searchGoodsByMeili(string $keyword, $userId = null, array $options = [])
    {
        try {
            if (empty($keyword)) {
                return self::getBubble($userId);
            }

            // 记录用户搜索关键词（条件：有用户ID且关键词长度>1）
            if (!empty($userId) && mb_strlen($keyword, 'UTF-8') > 1) {
                GoodsLogic::recordKeyword($keyword, $userId);
            }

            // 初始化MeiliSearch客户端
            $meili = new \app\common\library\MeiliSearch();

            // 测试连接
            if (!$meili->testConnection()) {
                \think\facade\Log::error('MeiliSearch连接失败，回退到数据库搜索');
                throw new \Exception('MeiliSearch连接失败');
            }

        // 1. NLP处理用户原始关键词
        $nlpProcessedInput = self::processKeywordWithNlp($keyword);
        // $nlpProcessedInput 结构示例:
        // [
        //     'core_query' => "手机", // NLP提取的核心搜索词，用于Meili的 q
        //     'filters' => ["brand_name = '苹果'", "attributes.颜色 = '红色'", "attributes.内存 = '128G'"], // 提取出的过滤条件
        //     'sort_logic' => ["sales_count:desc"], // 可选的、基于NLP理解的排序建议
        //     'raw_tokens' => ["红色", "苹果", "手机", "128G"] // 原始分词结果
        // ]

        $keywordForMeili = $nlpProcessedInput['core_query'] ?: $keyword; // 如果NLP未能提取核心查询，则使用原始查询

        // 设置默认搜索选项
        $defaultOptions = [
            'limit' => $options['limit'] ?? 10, // 优先使用传入的limit
            'offset' => $options['offset'] ?? 0, // 优先使用传入的offset
            'attributesToRetrieve' => ['id', 'name', 'image', 'remark', 'min_price', 'market_price', 'sales_actual', 'sales_virtual'],
            'attributesToHighlight' => ['name', 'remark'], // 考虑高亮NLP提取的关键词
            'highlightPreTag' => '<em>',
            'highlightPostTag' => '</em>'
        ];

        $searchOptions = array_merge($defaultOptions, $options); // 合并外部传入的options优先

        // 2. 应用NLP提取的过滤器
        if (!empty($nlpProcessedInput['filters'])) {
            if (isset($searchOptions['filter'])) {
                // 如果外部也传入了filter，需要合并逻辑
                // 这里简单示例：合并为AND。实际可能需要更复杂的合并策略
                $searchOptions['filter'] = array_merge((array)$searchOptions['filter'], $nlpProcessedInput['filters']);
            } else {
                $searchOptions['filter'] = $nlpProcessedInput['filters'];
            }
        }

        // 3. 应用NLP建议的排序逻辑 (如果外部没有指定排序)
        if (!isset($searchOptions['sort']) && !empty($nlpProcessedInput['sort_logic'])) {
            $searchOptions['sort'] = $nlpProcessedInput['sort_logic'];
        }

        // 4. 调整Meilisearch的匹配策略，要求更精确的匹配
        if (!isset($searchOptions['matchingStrategy']) && count($nlpProcessedInput['raw_tokens']) > 1) {
            $searchOptions['matchingStrategy'] = 'all'; // 要求所有查询词都匹配
        }

        \think\facade\Log::info("MeiliSearch Query: " . $keywordForMeili . " Params: " . json_encode($searchOptions)); // 日志记录

        // 执行搜索
        $results = $meili->advancedSearch('goods', $keywordForMeili, $searchOptions);

        // 处理搜索结果
        if (isset($results['hits']) && !empty($results['hits'])) {
            return [
                'list' => $results['hits'],
                'total' => $results['estimatedTotalHits'] ?? count($results['hits']),
                'processing_time_ms' => $results['processingTimeMs'] ?? 0
            ];
        }

        return ['list' => [], 'total' => 0, 'processing_time_ms' => 0];

        } catch (\Exception $e) {
            \think\facade\Log::error('MeiliSearch搜索失败: ' . $e->getMessage());

            // 回退到数据库搜索
            return self::fallbackDatabaseSearch($keyword, $userId, $options);
        }
    }

    /**
     * 使用阿里云NLP处理用户搜索关键词，提取核心查询、过滤器和排序逻辑
     * @param string $keyword 搜索关键词
     * @return array 处理结果
     */
    private static function processKeywordWithNlp(string $keyword): array
    {
        $coreQuery = $keyword; // 默认核心查询是原关键词
        $filters = [];
        $sortLogic = [];
        $rawTokens = [$keyword]; // 默认原始分词

        try {
            // 调用阿里云NLP
            $aliNlp = new \app\common\library\AliNlpService();

            // 1. 首先尝试使用中心词提取API
            $centerWordResult = $aliNlp->keywordExtractEcom($keyword);
            $centerWords = isset($centerWordResult['keywords']) ? $centerWordResult['keywords'] : [];

            // 2. 然后使用命名实体识别API
            $nerResult = $aliNlp->nerEcom($keyword);
            $nerWords = isset($nerResult['words']) ? $nerResult['words'] : [];

            // 3. 最后使用分词API
            $segmentResult = $aliNlp->segment($keyword);
            $segmentWords = isset($segmentResult['words']) ? $segmentResult['words'] : [];

            // 合并所有分词结果
            $allWords = array_merge($segmentWords, $nerWords, $centerWords);
            $rawTokens = array_unique($allWords);

            // 如果有中心词，优先使用中心词作为核心查询
            if (!empty($centerWords)) {
                $coreQuery = implode(' ', $centerWords);
            }

            // 尝试获取更详细的NLP分析结果
            $nlpWords = self::getDetailedNlpAnalysis($keyword);

            if (!empty($nlpWords)) {
                // 提取品牌、颜色、规格等作为过滤条件
                foreach ($nlpWords as $wordInfo) {
                    $word = $wordInfo['word'];
                    $pos = $wordInfo['pos']; // 词性

                    // 根据词性和词本身判断过滤条件
                    if (self::isBrand($word, $pos)) {
                        $filters[] = "brand_name = '" . addslashes($word) . "'";
                    } elseif (self::isColor($word, $pos)) {
                        $filters[] = "attributes.颜色 = '" . addslashes($word) . "'";
                    } elseif (self::isSpecification($word, $pos)) {
                        $specFilter = self::convertSpecToFilter($word);
                        if ($specFilter) $filters[] = $specFilter;
                    }
                }

                // 如果没有从中心词API获取到核心查询，尝试从词性分析中提取
                if ($coreQuery === $keyword && !empty($nlpWords)) {
                    $coreQueryParts = [];
                    foreach ($nlpWords as $wordInfo) {
                        if (in_array($wordInfo['pos'], ['n', 'nz', 'vn'])) { // 名词、专有名词、动名词
                            $coreQueryParts[] = $wordInfo['word'];
                        }
                    }
                    if (!empty($coreQueryParts)) {
                        $coreQuery = implode(' ', $coreQueryParts);
                    }
                }
            }
        } catch (\Exception $e) {
            \think\facade\Log::error("NLP processing for keyword '{$keyword}' failed: " . $e->getMessage());
        }

        return [
            'core_query' => $coreQuery,
            'filters' => $filters,
            'sort_logic' => $sortLogic,
            'raw_tokens' => $rawTokens
        ];
    }

    /**
     * 获取关键词的详细NLP分析结果 (分词、词性等)
     * @param string $keyword 搜索关键词
     * @return array 分析结果
     */
    protected static function getDetailedNlpAnalysis(string $keyword): array
    {
        try {
            $aliNlp = new \app\common\library\AliNlpService();

            // 使用词性标注API获取更详细的分析
            $posResult = $aliNlp->posTagging($keyword);

            if (isset($posResult['pos']) && !empty($posResult['pos'])) {
                $words = [];
                foreach ($posResult['pos'] as $item) {
                    $words[] = [
                        'word' => $item['word'] ?? '',
                        'pos' => $item['pos'] ?? ''
                    ];
                }
                return $words;
            }

            // 如果词性标注API失败，尝试使用分词API
            $segmentResult = $aliNlp->segment($keyword);
            if (isset($segmentResult['words']) && !empty($segmentResult['words'])) {
                $words = [];
                foreach ($segmentResult['words'] as $word) {
                    $words[] = [
                        'word' => $word,
                        'pos' => '' // 分词API不返回词性
                    ];
                }
                return $words;
            }
        } catch (\Exception $e) {
            \think\facade\Log::error("Detailed NLP analysis failed: " . $e->getMessage());
        }

        return [];
    }

    /**
     * 判断词是否为品牌
     * @param string $word 词
     * @param string $pos 词性
     * @return bool
     */
    protected static function isBrand(string $word, string $pos): bool
    {
        // 常见品牌词列表
        $commonBrands = ['华为', '苹果', '小米', '三星', '魅族', 'OPPO', 'vivo', '联想', '戴尔', '惠普',
                         '耐克', '阿迪达斯', '李宁', '安踏', '特步', '361°', '匡威', '彪马',
                         '海尔', '美的', '格力', '西门子', '松下', '索尼', '飞利浦', 'LG', '三洋'];

        // 如果词在常见品牌列表中，直接返回true
        if (in_array($word, $commonBrands)) {
            return true;
        }

        // 根据词性判断
        if ($pos === 'nz' || $pos === 'nt') { // 专有名词、机构名
            return true;
        }

        return false;
    }

    /**
     * 判断词是否为颜色
     * @param string $word 词
     * @param string $pos 词性（可选）
     * @return bool
     */
    protected static function isColor(string $word, string $pos = ''): bool
    {
        // 常见颜色词列表
        $colors = ['红', '黄', '蓝', '绿', '紫', '黑', '白', '灰', '粉', '棕', '橙', '青', '银', '金',
                  '红色', '黄色', '蓝色', '绿色', '紫色', '黑色', '白色', '灰色', '粉色', '棕色', '橙色', '青色', '银色', '金色',
                  '深红', '浅红', '深蓝', '浅蓝', '深绿', '浅绿', '深灰', '浅灰'];

        // 如果词性是形容词(a)，且词长小于3，可能是颜色词
        if ($pos === 'a' && mb_strlen($word, 'UTF-8') <= 3) {
            return true;
        }

        return in_array($word, $colors);
    }

    /**
     * 判断词是否为规格
     * @param string $word 词
     * @param string $pos 词性（可选）
     * @return bool
     */
    protected static function isSpecification(string $word, string $pos = ''): bool
    {
        // 检查是否包含数字+单位的模式，如"128G"、"5.5英寸"
        if (preg_match('/\d+(\.\d+)?(G|GB|T|TB|寸|英寸|cm|mm|kg|g|ml|L)$/u', $word)) {
            return true;
        }

        // 常见规格词列表
        $specs = ['大号', '中号', '小号', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', '加大', '加小',
                 '标准版', '豪华版', '简约版', '旗舰版', '入门版', '高配版', '低配版'];

        // 如果词性是区别词(b)，可能是规格词
        if ($pos === 'b') {
            return true;
        }

        return in_array($word, $specs);
    }

    /**
     * 将规格词转换为过滤条件
     * @param string $word 规格词
     * @return string|null 过滤条件
     */
    protected static function convertSpecToFilter(string $word): ?string
    {
        // 内存规格
        if (preg_match('/(\d+)(G|GB)$/u', $word, $matches)) {
            return "attributes.内存 = '" . $matches[1] . "GB'";
        }

        // 存储规格
        if (preg_match('/(\d+)(T|TB)$/u', $word, $matches)) {
            return "attributes.存储 = '" . $matches[1] . "TB'";
        }

        // 尺寸规格
        if (preg_match('/(\d+(\.\d+)?)(寸|英寸)$/u', $word, $matches)) {
            return "attributes.尺寸 = '" . $matches[1] . "英寸'";
        }

        // 重量规格
        if (preg_match('/(\d+(\.\d+)?)(kg|g)$/u', $word, $matches)) {
            $unit = $matches[3] === 'kg' ? '千克' : '克';
            return "attributes.重量 = '" . $matches[1] . $unit . "'";
        }

        // 容量规格
        if (preg_match('/(\d+(\.\d+)?)(ml|L)$/u', $word, $matches)) {
            $unit = $matches[3] === 'L' ? '升' : '毫升';
            return "attributes.容量 = '" . $matches[1] . $unit . "'";
        }

        // 尺码规格
        if (in_array($word, ['S', 'M', 'L', 'XL', 'XXL', 'XXXL'])) {
            return "attributes.尺码 = '" . $word . "'";
        }

        return null;
    }

    /**
     * 获取搜索建议
     *
     * @param string $keyword 搜索关键词
     * @param int $limit 返回结果数量
     * @return array 搜索建议列表
     */
    public static function getSearchSuggestions(string $keyword, int $limit = 10)
    {
        if (empty($keyword)) {
            return self::getHotKeywords($limit);
        }

        // 使用新的SearchSuggestionService获取更智能的搜索候选词
        try {
            $suggestionService = new \app\common\library\SearchSuggestionService();
            $rawSuggestions = $suggestionService->getSuggestions($keyword, $limit);

            // 转换为前端需要的格式（带高亮的title和不带高亮的name）
            $suggestions = [];
            foreach ($rawSuggestions as $suggestion) {
                // 获取原始文本
                $text = is_array($suggestion) && isset($suggestion['name']) ? $suggestion['name'] : $suggestion;

                // 提取文本中的关键部分，不加省略号
                $textWords = explode(' ', $text);
                $shortText = $textWords[0];

                // 如果第一个词太短，尝试添加第二个词
                if (count($textWords) > 1 && mb_strlen($shortText, 'UTF-8') < 5) {
                    $shortText .= ' ' . $textWords[1];
                }

                // 高亮处理关键词
                $pattern = '/(' . preg_quote($keyword, '/') . ')/iu';
                $replacement = '<b style="color: red;">$1</b>';
                $highlightedText = preg_replace($pattern, $replacement, $shortText);

                // 添加到结果数组
                $suggestions[] = [
                    'title' => $highlightedText, // 高亮后的简短文本
                    'name' => $shortText         // 简短的原始文本
                ];
            }

            // 如果新服务返回了有效的候选词，直接使用
            if (!empty($suggestions)) {
                return $suggestions;
            }
        } catch (\Exception $e) {
            // 记录错误，但不中断流程
            \think\facade\Log::error('SearchSuggestionService调用失败: ' . $e->getMessage());
        }

        // 如果新服务未返回有效候选词，使用原有逻辑获取候选词
        $rawSuggestions = self::getEcommerceStyleSuggestions($keyword, $limit);

        // 处理候选词，使其更简洁
        $suggestions = [];
        foreach ($rawSuggestions as $suggestion) {
            // 获取原始文本
            $text = isset($suggestion['name']) ? $suggestion['name'] : $suggestion;

            // 提取文本中的关键部分，不加省略号
            $textWords = explode(' ', $text);
            $shortText = $textWords[0];

            // 如果第一个词太短，尝试添加第二个词
            if (count($textWords) > 1 && mb_strlen($shortText, 'UTF-8') < 5) {
                $shortText .= ' ' . $textWords[1];
            }

            // 高亮处理关键词
            $pattern = '/(' . preg_quote($keyword, '/') . ')/iu';
            $replacement = '<b style="color: red;">$1</b>';
            $highlightedText = preg_replace($pattern, $replacement, $shortText);

            // 添加到结果数组
            $suggestions[] = [
                'title' => $highlightedText, // 高亮后的简短文本
                'name' => $shortText         // 简短的原始文本
            ];
        }

        // 如果没有找到候选词，返回热门关键词
        if (empty($suggestions)) {
            return self::getHotKeywords($limit);
        }

        return $suggestions;
    }

    /**
     * 获取电商风格的搜索候选词
     *
     * @param string $keyword 搜索关键词
     * @param int $limit 返回结果数量
     * @return array 搜索候选词列表
     */
    protected static function getEcommerceStyleSuggestions(string $keyword, int $limit = 10)
    {
        // 尝试使用阿里云NLP服务获取候选词
        $aliSuggestions = self::getAliNlpSuggestions($keyword, $limit);

        // 如果阿里云NLP服务返回了有效的候选词，直接使用
        if (!empty($aliSuggestions)) {
            return $aliSuggestions;
        }

        // 如果阿里云NLP服务未返回有效候选词，使用本地逻辑
        // 1. 直接从商品表中查询包含关键词的商品
        $goodsQuery = Db::name('goods')
            ->where('name', 'like', '%' . $keyword . '%')
            ->where('del', 0)
            ->where('status', 1)
            ->where('audit_status', 1)
            ->field('id, name, split_word, sales_actual, sales_virtual')
            ->limit(100)  // 获取足够多的商品以生成多样化的候选词
            ->select()
            ->toArray();

        if (empty($goodsQuery)) {
            // 如果没有找到商品，尝试使用分词查询
            $words = self::getSegmentedWords($keyword);
            $segmentedWords = !empty($words['words']) && is_array($words['words']) ? $words['words'] : [$keyword];

            $whereOr = [];
            foreach ($segmentedWords as $word) {
                if (mb_strlen(trim($word), 'UTF-8') >= 2) {
                    $whereOr[] = ['name', 'like', '%' . trim($word) . '%'];
                }
            }

            if (!empty($whereOr)) {
                $goodsQuery = Db::name('goods')
                    ->whereOr($whereOr)
                    ->where('del', 0)
                    ->where('status', 1)
                    ->where('audit_status', 1)
                    ->field('id, name, sales_actual, sales_virtual')
                    ->limit(100)
                    ->select()
                    ->toArray();
            }
        }

        if (empty($goodsQuery)) {
            return [];
        }

        // 2. 提取商品名称中的关键特征
        $features = [];
        $patterns = [
            // 性别特征
            '男' => ['男', '男士', '男式', '男装', '男鞋'],
            '女' => ['女', '女士', '女式', '女装', '女鞋'],
            '儿童' => ['儿童', '小孩', '宝宝', '童装', '童鞋'],

            // 季节特征
            '夏季' => ['夏季', '夏天', '夏装', '夏款'],
            '冬季' => ['冬季', '冬天', '冬装', '冬款'],
            '春季' => ['春季', '春天', '春装', '春款'],
            '秋季' => ['秋季', '秋天', '秋装', '秋款'],

            // 材质特征
            '棉' => ['棉', '纯棉', '棉质', '全棉'],
            '麻' => ['麻', '亚麻', '棉麻', '麻质'],
            '丝' => ['丝', '真丝', '丝绸', '丝质'],
            '毛' => ['毛', '羊毛', '毛呢', '毛绒'],
            '皮' => ['皮', '真皮', '皮质', '皮革'],

            // 款式特征
            '休闲' => ['休闲', '休闲款', '休闲型', '休闲风'],
            '运动' => ['运动', '运动款', '运动型', '运动风'],
            '商务' => ['商务', '商务款', '商务型', '商务风'],
            '宽松' => ['宽松', '宽松款', '宽松型', '宽松版'],
            '修身' => ['修身', '修身款', '修身型', '修身版'],
            '直筒' => ['直筒', '直筒款', '直筒型', '直筒版'],
            '阔腿' => ['阔腿', '阔腿款', '阔腿型', '阔腿版'],

            // 长度特征
            '长' => ['长', '长款', '长版', '长裤'],
            '短' => ['短', '短款', '短版', '短裤'],
            '中长' => ['中长', '中长款', '中长版'],

            // 颜色特征
            '黑色' => ['黑色', '黑', '纯黑'],
            '白色' => ['白色', '白', '纯白'],
            '蓝色' => ['蓝色', '蓝', '深蓝', '浅蓝'],
            '灰色' => ['灰色', '灰', '深灰', '浅灰'],

            // 其他特征
            '加绒' => ['加绒', '绒', '绒毛'],
            '加厚' => ['加厚', '厚', '厚款'],
            '薄款' => ['薄款', '薄', '轻薄'],
            '弹力' => ['弹力', '弹性', '有弹性'],
            '防水' => ['防水', '抗水', '不沾水'],
            '速干' => ['速干', '快干', '干爽']
        ];

        // 3. 分析商品名称，提取特征
        foreach ($goodsQuery as $goods) {
            $name = $goods['name'];

            // 检查商品名称中是否包含关键词
            if (mb_strpos($name, $keyword) !== false) {
                // 提取特征
                foreach ($patterns as $feature => $synonyms) {
                    foreach ($synonyms as $synonym) {
                        if (mb_strpos($name, $synonym) !== false) {
                            // 记录特征及其权重（销量越高权重越大）
                            $weight = ($goods['sales_actual'] + $goods['sales_virtual']) / 10;
                            if (!isset($features[$feature])) {
                                $features[$feature] = $weight;
                            } else {
                                $features[$feature] += $weight;
                            }
                            break;
                        }
                    }
                }
            }
        }

        // 4. 按权重排序特征
        arsort($features);

        // 5. 生成候选词组合
        $suggestions = [];

        // 首先添加关键词本身
        $suggestions[] = [
            'title' => '<b style="color: red;">' . $keyword . '</b>',
            'name' => $keyword
        ];

        // 生成"关键词+特征"组合
        foreach ($features as $feature => $weight) {
            $combination = $keyword . $feature;
            $highlightedText = '<b style="color: red;">' . $keyword . '</b>' . $feature;

            $suggestions[] = [
                'title' => $highlightedText,
                'name' => $combination
            ];

            // 检查是否已达到限制
            if (count($suggestions) >= $limit) {
                break;
            }
        }

        // 如果还不够，生成"特征+关键词"组合
        if (count($suggestions) < $limit) {
            foreach ($features as $feature => $weight) {
                $combination = $feature . $keyword;
                $highlightedText = $feature . '<b style="color: red;">' . $keyword . '</b>';

                // 避免重复
                $isDuplicate = false;
                foreach ($suggestions as $suggestion) {
                    if ($suggestion['name'] === $combination) {
                        $isDuplicate = true;
                        break;
                    }
                }

                if (!$isDuplicate) {
                    $suggestions[] = [
                        'title' => $highlightedText,
                        'name' => $combination
                    ];

                    // 检查是否已达到限制
                    if (count($suggestions) >= $limit) {
                        break;
                    }
                }
            }
        }

        // 6. 如果候选词数量仍然不足，添加一些常见的组合
        $commonCombinations = [
            '男' => $keyword . '男',
            '女' => $keyword . '女',
            '儿童' => $keyword . '儿童',
            '夏季' => $keyword . '夏季',
            '冬季' => $keyword . '冬季',
            '休闲' => $keyword . '休闲',
            '运动' => $keyword . '运动',
            '长款' => $keyword . '长款',
            '短款' => $keyword . '短款',
            '黑色' => $keyword . '黑色',
            '白色' => $keyword . '白色',
            '男士' => '男士' . $keyword,
            '女士' => '女士' . $keyword,
            '纯棉' => $keyword . '纯棉',
            '加绒' => $keyword . '加绒',
            '直筒' => $keyword . '直筒',
            '修身' => $keyword . '修身',
            '宽松' => $keyword . '宽松'
        ];

        if (count($suggestions) < $limit) {
            foreach ($commonCombinations as $feature => $combination) {
                // 避免重复
                $isDuplicate = false;
                foreach ($suggestions as $suggestion) {
                    if ($suggestion['name'] === $combination) {
                        $isDuplicate = true;
                        break;
                    }
                }

                if (!$isDuplicate) {
                    // 验证组合是否有效（能搜索到商品）
                    $count = self::countGoodsInMeili($combination);

                    if ($count > 0) {
                        // 高亮关键词
                        $highlightedText = str_replace(
                            $keyword,
                            '<b style="color: red;">' . $keyword . '</b>',
                            $combination
                        );

                        $suggestions[] = [
                            'title' => $highlightedText,
                            'name' => $combination
                        ];

                        // 检查是否已达到限制
                        if (count($suggestions) >= $limit) {
                            break;
                        }
                    }
                }
            }
        }

        // 7. 如果仍然不足，从商品名称中提取包含关键词的短语
        if (count($suggestions) < $limit) {
            $phrases = [];
            foreach ($goodsQuery as $goods) {
                $name = $goods['name'];

                // 如果商品名称包含关键词
                if (mb_strpos($name, $keyword) !== false) {
                    // 提取包含关键词的短语（最多10个字符）
                    $pos = mb_strpos($name, $keyword);
                    $start = max(0, $pos - 5);
                    $length = min(mb_strlen($name) - $start, 10);
                    $phrase = mb_substr($name, $start, $length);

                    // 确保短语包含关键词
                    if (mb_strpos($phrase, $keyword) !== false) {
                        $phrases[] = $phrase;
                    }
                }
            }

            // 去重
            $phrases = array_unique($phrases);

            // 添加到候选词
            foreach ($phrases as $phrase) {
                // 避免重复
                $isDuplicate = false;
                foreach ($suggestions as $suggestion) {
                    if ($suggestion['name'] === $phrase) {
                        $isDuplicate = true;
                        break;
                    }
                }

                if (!$isDuplicate) {
                    // 高亮关键词
                    $highlightedText = str_replace(
                        $keyword,
                        '<b style="color: red;">' . $keyword . '</b>',
                        $phrase
                    );

                    $suggestions[] = [
                        'title' => $highlightedText,
                        'name' => $phrase
                    ];

                    // 检查是否已达到限制
                    if (count($suggestions) >= $limit) {
                        break;
                    }
                }
            }
        }

        return $suggestions;
    }

    /**
     * 使用阿里云NLP服务获取搜索候选词
     *
     * @param string $keyword 搜索关键词
     * @param int $limit 返回结果数量
     * @return array 搜索候选词列表
     */
    protected static function getAliNlpSuggestions(string $keyword, int $limit = 10)
    {
        try {
            // 记录开始时间，用于性能分析
            $startTime = microtime(true);

            // 阿里云API配置
            $accessKeyId = ConfigServer::get('aliyun', 'access_key_id', '');
            $accessKeySecret = ConfigServer::get('aliyun', 'access_key_secret', '');

            // 如果没有配置阿里云API，直接返回空数组
            if (empty($accessKeyId) || empty($accessKeySecret)) {
                \think\facade\Log::info('阿里云API未配置，使用本地逻辑生成候选词');
                return [];
            }

            try {
                // 首先尝试使用SDK方式
                if (class_exists('\\AlibabaCloud\\Client\\AlibabaCloud')) {
                    try {
                        // 使用阿里云SDK调用NLP服务
                        \AlibabaCloud\Client\AlibabaCloud::accessKeyClient($accessKeyId, $accessKeySecret)
                            ->regionId('cn-hangzhou')
                            ->asDefaultClient();

                        // 检查是否存在NLP服务类
                        if (method_exists('\\AlibabaCloud\\Client\\AlibabaCloud', 'nlp')) {
                            // 调用阿里云NLP词法分析接口
                            $result = \AlibabaCloud\Client\AlibabaCloud::nlp()
                                ->v20180408()
                                ->wordSegment()
                                ->withDomain('general')
                                ->withText($keyword)
                                ->format('JSON')
                                ->request();

                            // SDK调用成功，直接返回结果
                            \think\facade\Log::info('使用SDK方式成功调用阿里云NLP服务');
                        } else {
                            throw new \Exception('阿里云NLP服务类不存在，可能SDK安装不完整');
                        }
                    } catch (\Exception $e) {
                        \think\facade\Log::warning('SDK方式调用阿里云NLP服务失败，尝试使用HTTP方式: ' . $e->getMessage());
                        // SDK方式失败，尝试使用HTTP方式
                        $result = null;
                    }
                } else {
                    \think\facade\Log::info('阿里云SDK未安装，尝试使用HTTP方式调用NLP服务');
                    $result = null;
                }

                // 如果SDK方式失败，尝试使用HTTP方式
                if (empty($result) && class_exists('\\app\\api\\logic\\AliyunNlpLogic')) {
                    try {
                        // 先尝试HTTP方式调用阿里云NLP服务
                        $nlpResult = \app\api\logic\AliyunNlpLogic::wordSegment($keyword);
                        if (!empty($nlpResult)) {
                            \think\facade\Log::info('使用HTTP方式成功调用阿里云NLP服务');
                            // 直接使用数组，不需要构造对象
                            $response = $nlpResult;
                            // 跳过后面的 $response = $result->toArray(); 语句
                            goto process_response;
                        } else {
                            // HTTP方式也失败，尝试使用本地分词逻辑
                            \think\facade\Log::info('HTTP方式调用阿里云NLP服务失败，尝试使用本地分词逻辑');
                            $localResult = \app\api\logic\AliyunNlpLogic::localWordSegment($keyword);
                            if (!empty($localResult)) {
                                \think\facade\Log::info('使用本地分词逻辑成功');
                                $response = $localResult;
                                // 跳过后面的 $response = $result->toArray(); 语句
                                goto process_response;
                            } else {
                                throw new \Exception('本地分词逻辑失败');
                            }
                        }
                    } catch (\Exception $e) {
                        \think\facade\Log::error('所有分词方式都失败: ' . $e->getMessage());
                        return [];
                    }
                }

                // 如果两种方式都失败，使用本地逻辑
                if (empty($result)) {
                    \think\facade\Log::info('阿里云NLP服务调用失败，使用本地逻辑生成候选词');
                    return [];
                }
            } catch (\Exception $e) {
                \think\facade\Log::error('阿里云NLP服务调用异常: ' . $e->getMessage());
                // 记录详细错误信息以便调试
                \think\facade\Log::error('错误详情: ' . $e->getTraceAsString());
                return [];
            }

            // 解析结果
            if (is_object($result) && method_exists($result, 'toArray')) {
                $response = $result->toArray();
            } else if (isset($response)) {
                // $response 已经在前面设置好了，不需要再处理
            } else {
                \think\facade\Log::error('无法解析NLP服务返回结果');
                return [];
            }

            // 处理响应的标记点
            process_response:

            // 记录API调用结果，便于调试
            \think\facade\Log::info('阿里云NLP词法分析结果: ' . json_encode($response));

            // 检查返回结果格式
            if (!is_array($response)) {
                \think\facade\Log::error('阿里云NLP服务返回结果格式错误: ' . json_encode($response));
                return [];
            }

            // 提取分词结果
            $words = [];

            // 处理不同格式的返回结果
            if (isset($response['Data']['result']) && !empty($response['Data']['result'])) {
                // SDK返回格式
                $words = $response['Data']['result'];
            } else if (isset($response['Data']['WordList']) && !empty($response['Data']['WordList'])) {
                // HTTP方式返回格式
                $wordList = $response['Data']['WordList'];
                foreach ($wordList as $item) {
                    $words[] = [
                        'word' => $item['Word'] ?? '',
                        'pos' => $item['Pos'] ?? ''
                    ];
                }
            } else {
                // 没有有效结果，使用本地逻辑
                \think\facade\Log::info('阿里云NLP服务未返回有效结果，使用本地逻辑生成候选词');
                return [];
            }

            // 根据分词结果生成候选词
            $suggestions = self::generateSuggestionsFromNlp($keyword, $words, $limit);

            // 记录执行时间
            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 4);
            \think\facade\Log::info("阿里云NLP候选词生成执行时间: {$executionTime} 秒");

            return $suggestions;
        } catch (\Exception $e) {
            // 记录错误，但不中断流程
            \think\facade\Log::error('阿里云NLP服务调用失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 根据阿里云NLP分词结果生成候选词
     *
     * @param string $keyword 原始搜索关键词
     * @param array $words 分词结果
     * @param int $limit 返回结果数量
     * @return array 候选词列表
     */
    protected static function generateSuggestionsFromNlp(string $keyword, array $words, int $limit = 10)
    {
        $suggestions = [];

        // 1. 首先添加原始关键词
        $originalSuggestionName = trim($keyword);
        $meiliOriginalCount = self::countGoodsInMeili($originalSuggestionName); // 使用Meili count

        if ($meiliOriginalCount > 0) { // 或者某个阈值
            $suggestions[$originalSuggestionName] = [
                'title' => '<b style="color: red;">' . htmlspecialchars($originalSuggestionName) . '</b>',
                'name' => $originalSuggestionName,
                'score' => 10000 + $meiliOriginalCount // 给予高基础分 + Meili匹配数
            ];
        }

        // 2. 提取词性和词
        $nouns = []; // 名词
        $adjectives = []; // 形容词
        $verbs = []; // 动词
        $attributes = []; // 属性词
        $brands = []; // 品牌词
        $categories = []; // 品类词

        foreach ($words as $word) {
            $text = $word['word'] ?? '';
            $pos = $word['pos'] ?? '';

            if (empty($text)) {
                continue;
            }

            // 根据词性分类
            if (!empty($pos)) {
                switch ($pos) {
                    case 'n': // 名词
                        $nouns[] = $text;
                        break;
                    case 'nz': // 专有名词
                        if (self::isBrand($text, $pos)) {
                            $brands[] = $text;
                        } else {
                            $categories[] = $text;
                        }
                        break;
                    case 'nr': // 人名
                        $nouns[] = $text;
                        break;
                    case 'ns': // 地名
                        $nouns[] = $text;
                        break;
                    case 'nt': // 机构名
                        $brands[] = $text;
                        break;
                    case 'a': // 形容词
                    case 'ad': // 副形词
                        $adjectives[] = $text;
                        break;
                    case 'v': // 动词
                    case 'vd': // 副动词
                        $verbs[] = $text;
                        break;
                    case 'b': // 区别词（属性词）
                        $attributes[] = $text;
                        break;
                }
            } else {
                // 如果没有词性信息，尝试根据词本身判断
                if (self::isBrand($text, '')) {
                    $brands[] = $text;
                } elseif (self::isColor($text, '')) {
                    $attributes[] = $text;
                } elseif (self::isSpecification($text, '')) {
                    $attributes[] = $text;
                } else {
                    // 默认作为名词处理
                    $nouns[] = $text;
                }
            }
        }

        // 3. 生成候选组合
        $candidateCombinations = [];

        // 3.1 品牌 + 品类组合 (优先级最高)
        foreach ($brands as $brand) {
            foreach ($categories as $category) {
                if ($brand !== $category) {
                    $candidateCombinations[] = $brand . $category;
                    $candidateCombinations[] = $category . $brand;
                }
            }

            // 品牌 + 关键词
            if ($brand !== $keyword) {
                $candidateCombinations[] = $brand . $keyword;
                $candidateCombinations[] = $keyword . $brand;
            }
        }

        // 3.2 名词 + 关键词组合
        foreach ($nouns as $noun) {
            if ($noun !== $keyword && mb_strlen($noun, 'UTF-8') >= 2) {
                $candidateCombinations[] = $noun . $keyword;
                $candidateCombinations[] = $keyword . $noun;
            }
        }

        // 3.3 形容词/属性词 + 关键词组合
        foreach (array_merge($adjectives, $attributes) as $attr) {
            if ($attr !== $keyword && mb_strlen($attr, 'UTF-8') >= 1) {
                $candidateCombinations[] = $attr . $keyword;
                $candidateCombinations[] = $keyword . $attr;
            }
        }

        // 3.4 动词 + 关键词组合
        foreach ($verbs as $verb) {
            if ($verb !== $keyword && mb_strlen($verb, 'UTF-8') >= 2) {
                $candidateCombinations[] = $verb . $keyword;
                $candidateCombinations[] = $keyword . $verb;
            }
        }

        // 3.5 品类 + 属性词组合
        foreach ($categories as $category) {
            foreach (array_merge($adjectives, $attributes) as $attr) {
                if ($category !== $attr) {
                    $candidateCombinations[] = $category . $attr;
                    $candidateCombinations[] = $attr . $category;
                }
            }
        }

        // 4. 使用Meilisearch验证候选组合
        foreach ($candidateCombinations as $comboStr) {
            if (mb_strlen($comboStr) > 20) continue; // 避免过长的建议
            if (isset($suggestions[$comboStr])) continue; // 已存在

            $meiliCount = self::countGoodsInMeili($comboStr);
            if ($meiliCount > 0) { // 或者某个阈值
                // 计算组合词的NLP词性权重得分
                $nlpScore = self::calculateNlpCombinationScore($comboStr, $words); // 新方法

                $suggestions[$comboStr] = [
                    'title' => self::highlightSuggestion($comboStr, $keyword), // 高亮方法
                    'name' => $comboStr,
                    'score' => $nlpScore + $meiliCount // 综合得分
                ];
            }

            // 如果已经有足够的候选词，就停止添加
            if (count($suggestions) >= $limit * 2) { // 获取更多，后面会排序和截取
                break;
            }
        }

        // 5. 如果候选词数量不足，添加一些常见的属性词组合
        if (count($suggestions) < $limit) {
            $commonAttributes = [
                '男', '女', '童', '男款', '女款',
                '夏', '春', '秋', '冬',
                '修身', '宽松', '直筒',
                '休闲', '运动', '商务',
                '新款', '经典', '时尚'
            ];

            foreach ($commonAttributes as $attr) {
                $combinedKeyword = $keyword . $attr;

                if (isset($suggestions[$combinedKeyword])) continue; // 已存在

                $meiliCount = self::countGoodsInMeili($combinedKeyword);
                if ($meiliCount > 0) {
                    $suggestions[$combinedKeyword] = [
                        'title' => self::highlightSuggestion($combinedKeyword, $keyword),
                        'name' => $combinedKeyword,
                        'score' => 500 + $meiliCount // 基础分较低 + Meili匹配数
                    ];
                }

                // 如果已经有足够的候选词，就停止添加
                if (count($suggestions) >= $limit * 2) {
                    break;
                }
            }
        }

        // 6. 按得分排序
        uasort($suggestions, function ($a, $b) {
            return $b['score'] <=> $a['score'];
        });

        // 7. 取前N个
        $finalSuggestions = array_slice($suggestions, 0, $limit);

        return array_values($finalSuggestions);
    }

    /**
     * 使用Meilisearch统计某个关键词组合能匹配到的商品数量
     * @param string $searchTerm 搜索词
     * @return int 匹配商品数量
     */
    protected static function countGoodsInMeili(string $searchTerm): int
    {
        if (empty(trim($searchTerm))) {
            return 0;
        }

        try {
            // 使用Meilisearch客户端
            $meili = new \app\common\library\MeiliSearch();

            // 执行搜索（searchInIndex方法不支持传递选项，直接使用关键词搜索）
            $result = $meili->searchInIndex('goods', $searchTerm);

            // 返回匹配数量
            return $result['estimatedTotalHits'] ?? $result['nbHits'] ?? 0;
        } catch (\Exception $exception) {
            \think\facade\Log::error("Meilisearch count failed for '{$searchTerm}': " . $exception->getMessage());

            // 如果Meilisearch查询失败，回退到数据库查询
            try {
                $count = Db::name('goods')
                    ->where(function($query) use ($searchTerm) {
                        $query->whereOr('name', 'like', '%' . $searchTerm . '%')
                              ->whereOr('split_word', 'like', '%' . $searchTerm . '%');
                    })
                    ->where('del', 0)
                    ->where('status', 1)
                    ->count();
                return $count;
            } catch (\Exception $dbError) {
                \think\facade\Log::error("Database count failed for '{$searchTerm}': " . $dbError->getMessage());
                return 0;
            }
        }
    }

    /**
     * 根据NLP分词结果和词性权重计算组合词的NLP分数
     * @param string $combination 组合词
     * @param array $originalNlpWords 原始NLP分词结果
     * @return int 分数
     */
    protected static function calculateNlpCombinationScore(string $combination, array $originalNlpWords): int
    {
        // 基础分数
        $score = 100;

        // 静态定义词性权重
        $tagWeights = [
            // 高优先级
            'nz' => 30, // 专有名词
            'n' => 25,  // 名词
            'nt' => 22, // 机构名
            'ns' => 20, // 地名
            'nr' => 18, // 人名

            // 中优先级
            'a' => 15,  // 形容词
            'b' => 15,  // 区别词
            'v' => 12,  // 动词
            'vn' => 10, // 动名词

            // 低优先级
            'ad' => 5,  // 副形词
            'vd' => 5,  // 副动词
            'd' => 3,   // 副词
            'r' => 2,   // 代词
            'c' => 1,   // 连词
        ];

        // 遍历原始NLP分词结果，查找组合词中包含的词
        foreach ($originalNlpWords as $wordInfo) {
            $word = $wordInfo['word'] ?? '';
            $pos = $wordInfo['pos'] ?? '';

            if (empty($word)) continue;

            // 如果组合词中包含该词
            if (mb_strpos($combination, $word) !== false) {
                // 根据词性增加分数
                if (!empty($pos) && isset($tagWeights[$pos])) {
                    $score += $tagWeights[$pos];
                }

                // 根据词长增加分数（长词通常更有意义）
                $score += min(10, mb_strlen($word, 'UTF-8') * 2);
            }
        }

        // 根据组合词长度适当调整分数（避免过长的组合词）
        $comboLength = mb_strlen($combination, 'UTF-8');
        if ($comboLength > 10) {
            $score -= ($comboLength - 10) * 5; // 每超过10个字符，减5分
        }

        return max(0, $score); // 确保分数不为负
    }

    /**
     * 高亮显示搜索关键词
     * @param string $suggestion 候选词
     * @param string $keyword 原始搜索关键词
     * @return string 高亮后的文本
     */
    protected static function highlightSuggestion(string $suggestion, string $keyword): string
    {
        // 使用正则表达式进行大小写不敏感的匹配
        $pattern = '/(' . preg_quote(htmlspecialchars($keyword), '/') . ')/iu';
        $replacement = '<b style="color: red;">$1</b>';

        // 先对文本进行HTML转义，防止XSS
        $safeText = htmlspecialchars($suggestion);

        // 检查关键词是否在文本中
        if (mb_stripos($safeText, htmlspecialchars($keyword)) !== false) {
            // 如果在，高亮显示
            $highlighted = preg_replace($pattern, $replacement, $safeText);
            return $highlighted ?: $safeText; // 如果替换失败，返回原文本
        } else {
            // 如果不在，在开头添加高亮的关键词
            return '<b style="color: red;">' . htmlspecialchars($keyword) . '</b>' . $safeText;
        }
    }

    /**
     * 验证搜索建议的有效性
     *
     * @param array $suggestions 搜索建议列表
     * @return array 验证后的搜索建议列表
     */
    protected static function validateSuggestions(array $suggestions)
    {
        $validSuggestions = [];

        foreach ($suggestions as $suggestion) {
            // 获取建议文本
            $text = is_array($suggestion) ? ($suggestion['name'] ?? '') : $suggestion;

            if (empty($text)) {
                continue;
            }

            // 使用Meilisearch验证候选词是否有对应商品
            $meiliCount = self::countGoodsInMeili($text);

            if ($meiliCount > 0) {
                // 如果建议有效，添加到结果中
                $validSuggestions[] = $suggestion;
            }
        }

        return $validSuggestions;
    }

    /**
     * 检查搜索建议对应的商品是否存在
     *
     * @param string $suggestion 搜索建议
     * @return bool 是否存在对应商品
     */
    protected static function checkSuggestionExists(string $suggestion)
    {
        // 使用Meilisearch检查是否有包含该建议的商品
        $meiliCount = self::countGoodsInMeili($suggestion);

        return $meiliCount > 0;
    }





    /**
     * 生成基础词的组合
     * @param array $baseWord
     * @param array $groupedWords
     * @param array $optionalTags
     * @param int $count
     * @return array
     */
    protected function generateCombinationsForBaseWord(array $baseWord, array $groupedWords, array $optionalTags, int $count): array
    {
        $combinations = [];
        $optionalWords = [];

        // 收集可选词
        foreach ($optionalTags as $tag) {
            if (isset($groupedWords[$tag])) {
                $optionalWords = array_merge($optionalWords, $groupedWords[$tag]);
            }
        }

        // 生成组合
        $combinations = $this->combineWords($baseWord, $optionalWords, $count);

        return $combinations;
    }
    /**
     * 按词性分组
     * @param array $words
     * @return array
     */
    protected function groupWordsByTags(array $words): array
    {
        $grouped = [];
        foreach ($words as $word) {
            // 确保每个词都有tags字段
            $tags = isset($word['tags']) ? explode(',', $word['tags']) : [];

            foreach ($tags as $tag) {
                if (!isset($grouped[$tag])) {
                    $grouped[$tag] = [];
                }

                $grouped[$tag][] = [
                    'word' => $word['word'],
                    'weight' => $this->tagWeights[$tag] ?? 0,
                    'tags' => $tags // 确保每个词都包含tags信息
                ];
            }
        }
        return $grouped;
    }
    /**
     * 获取组合
     * @param array $baseWord
     * @param array $optionalWords
     * @param array $indices
     * @return array
     */
    protected function getCombinations(array $baseWord, array $optionalWords, array $indices): array
    {
        $combinations = [];
        $combinations = array_merge(
            $combinations,
            $this->getWeightedCombination($baseWord, $optionalWords, $indices)
        );

        return $combinations;
    }
    /**
     * 根据用户输入生成候选词
     * @param string $input 用户输入的关键词
     * @param int $limit 返回数量
     * @return array
     */
    public function generateCombinations(string $input, int $limit = 10): array
    {
        // 1. 查找匹配的分词
        $words = Db::name('word')
            ->where('word', 'like', $input . '%')
            ->order('word', 'asc')
            ->limit(100)
            ->select()->toArray();

        if (empty($words)) {
            return [];
        }

        // 2. 获取匹配分词的ID
        $wordIds = array_column($words, 'id');

        // 3. 通过product_word表找到关联的商品ID
        $goodsIds = Db::name('product_word')
            ->whereIn('word_id', $wordIds)
            ->column('goods_id');

        if (empty($goodsIds)) {
            return [];
        }

        // 4. 获取这些商品关联的所有分词
        $allWords = Db::name('product_word')
            ->alias('pw')
            ->join('word w', 'pw.word_id = w.id')
            ->whereIn('pw.goods_id', $goodsIds)
            ->field('w.*')
            ->limit(200) // 限制最大数量
            ->select()
            ->toArray();

        // 5. 按词性分组
        $groupedWords = $this->groupWordsByTags($allWords);

        // 6. 定义基础词性和扩展词性
        $baseTags = [
            '品牌', '品类', '功能功效', '人群', '场景'
        ];

        $extendTags = [
            '型号', '尺寸规格', '颜色', '风格', '材质',
            '款式元素', '系列', '营销服务', '赠送', '后缀',
            '功能功效', '场景', '修饰', '单位'
        ];

        // 7. 生成候选词
        $combinations = [];
        foreach ($baseTags as $tag) {
            if (isset($groupedWords[$tag])) {
                foreach ($groupedWords[$tag] as $baseWord) {
                    // 生成2-4个词的组合
                    for ($i = 1; $i <= 3; $i++) {
                        $combinations = array_merge(
                            $combinations,
                            $this->generateMultiWordCombinations($baseWord, $groupedWords, $extendTags, $i)
                        );
                    }
                }
            }
        }

        // 8. 过滤和排序
        return $this->filterAndSortCombinations($input, $combinations, $limit);
    }


    /**
     * 生成多词组合
     * @param array $baseWord 基础词
     * @param array $groupedWords 分组后的词
     * @param array $extendTags 扩展词性
     * @param int $wordCount 组合词数
     * @return array
     */
    protected function generateMultiWordCombinations(array $baseWord, array $groupedWords, array $extendTags, int $wordCount): array
    {
        $combinations = [];
        $extendWords = [];

        // 收集扩展词
        foreach ($extendTags as $tag) {
            if (isset($groupedWords[$tag])) {
                $extendWords = array_merge($extendWords, $groupedWords[$tag]);
            }
        }

        // 打乱顺序，增加随机性
        shuffle($extendWords);

        // 生成组合
        $combinations = $this->combineWords($baseWord, $extendWords, $wordCount);

        return $combinations;
    }

    /**
     * 生成后缀组合
     * @param array $baseWord 基础词
     * @param array $groupedWords 分组后的词
     * @param array $suffixTags 后缀词性
     * @param int $maxCombinations 最大组合数
     * @return array
     */
    protected function generateSuffixCombinations(array $baseWord, array $groupedWords, array $suffixTags, int $maxCombinations): array
    {
        $combinations = [];
        $suffixWords = [];

        // 收集后缀词
        foreach ($suffixTags as $tag) {
            if (isset($groupedWords[$tag])) {
                $suffixWords = array_merge($suffixWords, $groupedWords[$tag]);
            }
        }

        // 打乱顺序，增加随机性
        shuffle($suffixWords);

        // 生成组合
        for ($i = 0; $i < min($maxCombinations, count($suffixWords)); $i++) {
            $suffix = $suffixWords[$i];
            $combinations[] = [
                'text' => $baseWord['word'] . ' ' . $suffix['word'],
                'weight' => $baseWord['weight'] + $suffix['weight'],
                'tags' => array_unique(array_merge($baseWord['tags'], $suffix['tags']))
            ];
        }

        return $combinations;
    }

// ... existing code ...

    /**
     * 过滤和排序组合
     * @param string $input
     * @param array $combinations
     * @param int $limit
     * @return array
     */
    protected function filterAndSortCombinations(string $input, array $combinations, int $limit): array
    {
        // 确保数组结构正确
        $validCombinations = array_filter($combinations, function($item) {
            return isset($item['text']) && isset($item['weight']);
        });

        // 过滤
        $filtered = array_filter($validCombinations, function($item) use ($input) {
            $text = $item['text'];

            // 1. 必须包含用户输入的关键词
            if (mb_strpos($text, $input) === false) {
                return false;
            }

            // 2. 长度限制放宽到30个字符
            if (mb_strlen($text) > 30) {
                return false;
            }

            // 3. 排除重复的空格
            if (preg_match('/\s{2,}/', $text)) {
                return false;
            }

            return true;
        });

        // 排序
        usort($filtered, function($a, $b) use ($input) {
            // 1. 按匹配度排序（开头匹配优先）
            $aType = $this->getMatchType($a['text'], $input);
            $bType = $this->getMatchType($b['text'], $input);
            if ($aType !== $bType) {
                return $aType <=> $bType;
            }

            // 2. 按权重排序
            if ($a['weight'] !== $b['weight']) {
                return $b['weight'] <=> $a['weight'];
            }

            // 3. 按长度排序
            return mb_strlen($a['text']) <=> mb_strlen($b['text']);
        });

        // 取前N个
        $result = array_slice($filtered, 0, $limit);

        // 只返回文本
        return array_column($result, 'text');
    }




    /**
     * 获取随机索引
     * @param int $total 总数
     * @param int $count 需要数量
     * @return array
     */
    protected function getRandomIndices(int $total, int $count): array
    {
        if ($total <= $count) {
            return range(0, $total - 1);
        }

        $indices = range(0, $total - 1);
        shuffle($indices);
        return array_slice($indices, 0, $count);
    }







    /**
     * 获取匹配类型
     * @param string $text
     * @param string $input
     * @return int
     */
    protected function getMatchType(string $text, string $input): int
    {
        // 0: 开头匹配（最高优先级）
        if (mb_strpos($text, $input) === 0) {
            return 0;
        }

        // 1: 包含匹配（次优先级）
        if (mb_strpos($text, $input) !== false) {
            return 1;
        }

        // 2: 其他匹配（最低优先级）
        return 2;
    }














    /**
     * 根据用户搜索关键词生成候选词
     * @param string $input 用户输入的关键词
     * @param array $words 分词列表（格式：[["text" => "分词", "tags" => "词性1,词性2"], ...]）
     * @return array
     */
    public function generateCandidates(string $input, array $words): array
    {
        $candidates = [];

        // 1. 找到与用户输入匹配的基础词
        $baseWords = $this->findBaseWords($input, $words);

        // 2. 找到与基础词关联的扩展词
        foreach ($baseWords as $baseWord) {
            $extendWords = $this->findExtendWords($baseWord, $words);

            // 3. 生成组合词
            $combinations = $this->combineWords($baseWord, $extendWords, 10); // 最多生成10个组合
            $candidates = array_merge($candidates, $combinations);
        }

        // 4. 过滤和排序候选词
        $candidates = $this->filterAndSortCandidates($candidates);

        return $candidates;
    }
    /**
     * 过滤和排序候选词
     * @param array $candidates
     * @return array
     */
    protected function filterAndSortCandidates(array $candidates): array
    {
        // 按权重排序
        usort($candidates, function ($a, $b) {
            return $b['weight'] <=> $a['weight'];
        });

        // 去重
        $uniqueCandidates = [];
        foreach ($candidates as $candidate) {
            $text = $candidate['text'];
            if (!isset($uniqueCandidates[$text])) {
                $uniqueCandidates[$text] = $candidate;
            }
        }

        return array_values($uniqueCandidates);
    }
    protected function findBaseWords(string $input, array $words): array
    {
        $baseWords = array_filter($words, function ($word) use ($input) {
            // 检查分词是否包含用户输入
            $isMatch = mb_strpos($word['text'], $input) !== false;

            // 检查词性是否匹配高优先级
            $tags = explode(',', $word['tags']);
            $isHighPriority = array_intersect($tags, ['产品类型-简单', '产品-品牌', '产品类型属性词']);



            return $isMatch && !empty($isHighPriority);
        });


        return $baseWords;
    }

    protected function findExtendWords(array $baseWord, array $words): array
    {
        $extendWords = array_filter($words, function ($word) use ($baseWord) {
            // 排除自身
            if ($word['text'] === $baseWord['text']) {
                return false;
            }

            // 检查词性权重
            $tags = explode(',', $word['tags']);
            foreach ($tags as $tag) {
                if (isset($this->tagWeights[$tag]) && $this->tagWeights[$tag] > 0) {
                    return true;
                }
            }

            // 调试日志
            error_log("排除分词: " . $word['text'] . ", 词性: " . $word['tags']);

            return false;
        });

        // 调试日志
        error_log("找到的扩展词数量: " . count($extendWords));

        return $extendWords;
    }
    /**
     * 组合词
     * @param array $baseWord 基础词
     * @param array $extendWords 扩展词
     * @param int $wordCount 组合词数
     * @return array
     */
    protected function combineWords(array $baseWord, array $extendWords, int $wordCount): array
    {
        $combinations = [];

        // 确保至少有一个扩展词
        if (empty($extendWords)) {
            return [];
        }

        // 去重扩展词
        $uniqueExtendWords = array_values(array_unique($extendWords, SORT_REGULAR));

        // 按权重排序扩展词
        usort($uniqueExtendWords, function ($a, $b) {
            $aWeight = $this->calculateWordWeight($a);
            $bWeight = $this->calculateWordWeight($b);
            return $bWeight <=> $aWeight;
        });

        // 限制最大组合数
        $maxCombinations = min($wordCount, count($uniqueExtendWords));

        // 生成组合
        $usedWords = []; // 记录已使用的扩展词
        foreach ($uniqueExtendWords as $extendWord) {
            if (count($combinations) >= $maxCombinations) {
                break;
            }

            // 避免重复组合
            if (in_array($extendWord['text'], $usedWords)) {
                continue;
            }

            $combination = $this->getWeightedCombination($baseWord, $extendWord);
            if ($this->isValidCombination($combination, $baseWord['text'])) {
                $combinations[] = $combination;
                $usedWords[] = $extendWord['text']; // 标记为已使用
            }
        }

        return $combinations;
    }

    /**
     * 计算分词权重
     * @param array $word
     * @return int
     */
    protected function calculateWordWeight(array $word): int
    {
        $tags = explode(',', $word['tags']);
        $weight = 0;

        foreach ($tags as $tag) {
            if (isset($this->tagWeights[$tag])) {
                $weight += $this->tagWeights[$tag];
            }
        }

        return $weight;
    }

    /**
     * 获取加权组合
     * @param array $baseWord 基础词
     * @param array $optionalWord 扩展词
     * @return array
     */
    protected function getWeightedCombination(array $baseWord, array $optionalWord): array
    {
        return [
            'text' => $baseWord['text'] . ' ' . $optionalWord['text'],
            'weight' => $this->calculateWordWeight($baseWord) + $this->calculateWordWeight($optionalWord),
            'tags' => array_unique(array_merge(explode(',', $baseWord['tags']), explode(',', $optionalWord['tags'])))
        ];
    }

    /**
     * 检查组合是否有效
     * @param array $combination
     * @param string $baseWord 基础词
     * @return bool
     */
    protected function isValidCombination(array $combination, string $baseWord): bool
    {
        // 1. 检查文本是否重复
        $text = $combination['text'];
        if (mb_substr_count($text, $baseWord) > 1) {
            return false;
        }

        // 2. 检查长度
        if (mb_strlen($text) > 20) {
            return false;
        }

        // 3. 检查词性是否合理
        $tags = $combination['tags'];
        if (empty($tags) || !is_array($tags)) {
            return false;
        }

        // 4. 排除不合理的组合（如“老人 老人”）
        if ($combination['text'] === $baseWord . ' ' . $baseWord) {
            return false;
        }

        return true;
    }







}